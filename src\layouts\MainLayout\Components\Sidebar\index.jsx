import React from "react";
import { Sidebar as ProSidebar, Menu, MenuItem, SubMenu } from "react-pro-sidebar";
import { Link, useLocation } from "react-router-dom";
import { useSelector } from "react-redux";
import {
  FaHome,
  FaTruck,
  FaBoxOpen,
  FaRoute,
  FaWarehouse,
  FaChartBar,
  FaUsers,
  FaCog,
  FaUser,
  FaMapMarkedAlt,
  FaClipboardList,
  FaFileInvoiceDollar,
  FaUserTie
} from "react-icons/fa";
import { TriTrackzLogo } from "@components/LogisticsIcons";
import ROUTES from "@constants/routes";

const Sidebar = ({ collapsed, toggled, onBackdropClick }) => {
  const location = useLocation();
  const user = useSelector((state) => state.user.user);

  return (
    <ProSidebar
      className="main-sidebar"
      breakPoint="lg"
      width="280px"
      collapsedWidth="80px"
      collapsed={collapsed}
      toggled={toggled}
      onBackdropClick={onBackdropClick}
      backgroundColor="var(--white)"
      rootStyles={{
        border: 'none',
        boxShadow: '2px 0 10px rgba(0, 0, 0, 0.1)',
      }}
    >
      <div className="sidebar-content h-100 d-flex flex-column">
        {/* Logo Section */}
        <div className="sidebar-header p-3 border-bottom">
          <div className={`d-flex align-items-center ${collapsed ? 'justify-content-center' : 'justify-content-start'}`}>
            {!collapsed ? (
              <div className="d-flex align-items-center">
                <TriTrackzLogo size={40} className="me-2" />
                <div>
                  <h5 className="mb-0 text-primary fw-bold">TRITRACKZ</h5>
                  <small className="text-muted">Logistics Hub</small>
                </div>
              </div>
            ) : (
              <div className="d-flex justify-content-center">
                <TriTrackzLogo size={40} />
              </div>
            )}
          </div>
        </div>

        {/* Navigation Menu */}
        <Menu
          className="flex-grow-1"
          menuItemStyles={{
            button: {
              padding: collapsed ? '12px 8px' : '12px 20px',
              margin: collapsed ? '4px 8px' : '4px 12px',
              borderRadius: '8px',
              color: 'var(--neutral-grey)',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease',
              justifyContent: collapsed ? 'center' : 'flex-start',
              '&:hover': {
                backgroundColor: 'var(--light-grey)',
                color: 'var(--primary-blue)',
              },
              '&.ps-active': {
                backgroundColor: 'var(--secondary-blue)',
                color: 'white',
                boxShadow: '0 4px 12px rgba(59, 130, 246, 0.3)',
              },
            },
            icon: {
              marginRight: collapsed ? '0' : '12px',
              minWidth: '20px',
              display: 'flex',
              justifyContent: 'center',
            },
            label: {
              display: collapsed ? 'none' : 'block',
            },
            subMenuContent: {
              backgroundColor: 'var(--light-grey)',
            },
          }}
        >
          <MenuItem
            icon={<FaHome size={18} />}
            active={location.pathname === ROUTES.HOME}
            component={<Link to={ROUTES.HOME} />}
            title={collapsed ? "Dashboard" : ""}
          >
            {!collapsed && "Dashboard"}
          </MenuItem>

          <SubMenu 
            icon={<FaTruck size={18} />} 
            label={!collapsed ? "Fleet Management" : ""}
            title={collapsed ? "Fleet Management" : ""}
          >
            <MenuItem 
              icon={<FaTruck size={16} />}
              title={collapsed ? "Vehicles" : ""}
            >
              {!collapsed && "Vehicles"}
            </MenuItem>
            <MenuItem 
              icon={<FaUserTie size={16} />}
              title={collapsed ? "Drivers" : ""}
            >
              {!collapsed && "Drivers"}
            </MenuItem>
            <MenuItem 
              icon={<FaRoute size={16} />}
              title={collapsed ? "Routes" : ""}
            >
              {!collapsed && "Routes"}
            </MenuItem>
          </SubMenu>

          <SubMenu 
            icon={<FaBoxOpen size={18} />} 
            label={!collapsed ? "Shipments" : ""}
            title={collapsed ? "Shipments" : ""}
          >
            <MenuItem 
              icon={<FaClipboardList size={16} />}
              title={collapsed ? "All Shipments" : ""}
            >
              {!collapsed && "All Shipments"}
            </MenuItem>
            <MenuItem 
              icon={<FaMapMarkedAlt size={16} />}
              title={collapsed ? "Track Shipments" : ""}
            >
              {!collapsed && "Track Shipments"}
            </MenuItem>
            <MenuItem 
              icon={<FaFileInvoiceDollar size={16} />}
              title={collapsed ? "Invoices" : ""}
            >
              {!collapsed && "Invoices"}
            </MenuItem>
          </SubMenu>

          <MenuItem 
            icon={<FaWarehouse size={18} />}
            title={collapsed ? "Warehouses" : ""}
          >
            {!collapsed && "Warehouses"}
          </MenuItem>

          <MenuItem 
            icon={<FaChartBar size={18} />}
            title={collapsed ? "Analytics" : ""}
          >
            {!collapsed && "Analytics"}
          </MenuItem>

          <MenuItem 
            icon={<FaUsers size={18} />}
            title={collapsed ? "Customers" : ""}
          >
            {!collapsed && "Customers"}
          </MenuItem>

          <MenuItem 
            icon={<FaCog size={18} />}
            title={collapsed ? "Settings" : ""}
          >
            {!collapsed && "Settings"}
          </MenuItem>
        </Menu>

        {/* User Section */}
        <div className="sidebar-footer p-3 border-top">
          {!collapsed ? (
            <div className="d-flex align-items-center">
              <div className="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3" style={{ width: '40px', height: '40px' }}>
                <FaUser size={16} />
              </div>
              <div className="flex-grow-1">
                <div className="fw-semibold text-dark">{user?.name || 'User'}</div>
                <small className="text-muted">{user?.email || '<EMAIL>'}</small>
              </div>
            </div>
          ) : (
            <div className="d-flex justify-content-center">
              <div className="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center" style={{ width: '40px', height: '40px' }} title={user?.name || 'User'}>
                <FaUser size={16} />
              </div>
            </div>
          )}
        </div>
      </div>
    </ProSidebar>
  );
};

export default Sidebar;

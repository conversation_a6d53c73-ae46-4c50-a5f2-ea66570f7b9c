/* TriTrackz Logistics Theme - Common Styles */

/* ===== CSS VARIABLES ===== */
:root {
  /* Logistics Color Palette */
  --primary-blue: #1e40af;
  --secondary-blue: #3b82f6;
  --accent-orange: #f97316;
  --neutral-grey: #64748b;
  --light-grey: #f1f5f9;
  --dark-grey: #334155;
  --success-green: #10b981;
  --white: #ffffff;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-heavy: rgba(0, 0, 0, 0.25);
}

/* ===== GLOBAL OVERRIDES ===== */
.bg-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
}

/* ===== COMMON ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ===== COMMON FORM STYLES ===== */
.form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.form-control.ps-5 {
  padding-left: 3rem !important;
}

.form-control.pe-5 {
  padding-right: 3rem !important;
}

.position-relative .position-absolute {
  pointer-events: none;
}

.position-relative button.position-absolute {
  pointer-events: auto;
  z-index: 10;
}

.form-check-input:checked {
  background-color: var(--secondary-blue);
  border-color: var(--secondary-blue);
}

.form-check-input:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* ===== COMMON BUTTON STYLES ===== */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

.btn-outline-primary {
  border: 2px solid var(--secondary-blue);
  color: var(--secondary-blue);
  background: transparent;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  background: var(--secondary-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.btn-outline-primary:disabled {
  border-color: #94a3b8;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

.btn-link {
  border: none !important;
  text-decoration: none !important;
  transition: all 0.3s ease;
}

.btn-link:hover {
  color: var(--primary-blue) !important;
  transform: translateY(-1px);
}

/* ===== COMMON LINK STYLES ===== */
a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}

/* ===== COMMON CARD STYLES ===== */
.card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 25px 50px -12px var(--shadow-medium);
  transform: translateY(-2px);
}

/* ===== COMMON DROPDOWN STYLES ===== */
.dropdown-menu {
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.dropdown-header {
  color: var(--dark-grey);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.5rem 1rem;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
}

.dropdown-item:hover {
  background: var(--light-grey);
  color: var(--primary-blue);
  transform: translateX(4px);
}

.dropdown-item.text-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* ===== COMMON BADGE STYLES ===== */
.badge.bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

/* ===== COMMON USER AVATAR STYLES ===== */
.user-avatar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* ===== COMMON LOADING STYLES ===== */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

.loading {
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 1.5s infinite;
}

/* ===== COMMON ACCESSIBILITY STYLES ===== */
button:focus,
.form-control:focus,
.dropdown-toggle:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* ===== COMMON SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--light-grey);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-grey);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-grey);
}

/* ===== COMMON RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .card {
    padding: 1rem;
    margin: 1rem;
  }
}

@media (max-width: 576px) {
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }

  .card {
    padding: 1rem;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .btn,
  .dropdown,
  .navbar,
  .sidebar {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}



.auth-left-panel {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  position: relative;
  overflow: hidden;
}

.auth-left-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.auth-brand-container {
  position: relative;
  z-index: 2;
}

.auth-brand-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.auth-brand-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-weight: 300;
}

.logistics-icons {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.logistics-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: iconFloat 3s ease-in-out infinite;
}

.logistics-icon:nth-child(2) {
  animation-delay: 0.5s;
}

.logistics-icon:nth-child(3) {
  animation-delay: 1s;
}

.logistics-icon:nth-child(4) {
  animation-delay: 1.5s;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

.logistics-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px) scale(1.1);
}

.logistics-icon svg {
  width: 28px;
  height: 28px;
  color: var(--white);
}

/* Right Panel Styles */
.auth-right-panel {
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  transition: all 0.3s ease;
}

.auth-card:hover {
  box-shadow: 0 25px 50px -12px var(--shadow-medium);
  transform: translateY(-2px);
}

/* Form Styles */
.login-form .form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.login-form .form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.login-form .form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Input with Icons Styling */
.login-form .form-control.ps-5 {
  padding-left: 3rem !important;
}

.login-form .form-control.pe-5 {
  padding-right: 3rem !important;
}

.login-form .position-relative .position-absolute {
  pointer-events: none;
}

.login-form .position-relative button.position-absolute {
  pointer-events: auto;
  z-index: 10;
}

.login-form .position-relative button.position-absolute:hover {
  color: var(--secondary-blue) !important;
}

.login-form .position-relative button.position-absolute:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
  border-radius: 4px;
}

.login-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-form .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-form .btn-primary:hover::before {
  left: 100%;
}

.login-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.login-form .btn-primary:active {
  transform: translateY(0);
}

/* Welcome Text Styles */
.welcome-text h2 {
  color: var(--dark-grey);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.welcome-text p {
  color: var(--neutral-grey);
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* Links and Interactive Elements */
.login-form a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.login-form a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}

.form-check-input:checked {
  background-color: var(--secondary-blue);
  border-color: var(--secondary-blue);
}

.form-check-input:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-brand-title {
    font-size: 2.5rem;
  }

  .auth-brand-subtitle {
    font-size: 1rem;
  }

  .logistics-icons {
    gap: 1rem;
    margin-top: 2rem;
  }

  .logistics-icon {
    width: 50px;
    height: 50px;
  }

  .logistics-icon svg {
    width: 24px;
    height: 24px;
  }

  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .welcome-text h2 {
    font-size: 1.75rem;
  }
}

@media (max-width: 576px) {
  .auth-brand-title {
    font-size: 2rem;
  }

  .auth-card {
    padding: 1.5rem;
  }

  .logistics-icons {
    gap: 0.75rem;
  }

  .logistics-icon {
    width: 45px;
    height: 45px;
  }
}

/* Loading Animation */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Accessibility Improvements */
.auth-card:focus-within {
  box-shadow: 0 25px 50px -12px var(--shadow-medium), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Additional Professional Enhancements */
.auth-layout {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.auth-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-brand-container {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Input Focus States */
.login-form .form-control:focus {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Professional Loading State */
.login-form .btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.login-form .btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Enhanced Mobile Experience */
@media (max-width: 768px) {
  .auth-layout {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  }

  .auth-right-panel {
    background: transparent;
    padding: 1rem;
  }

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* Improved Accessibility */
.logistics-icon:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

.login-form .form-control:focus {
  outline: none;
}

.login-form .btn-primary:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Professional Hover Effects */
.auth-card .form-control:hover {
  border-color: #cbd5e1;
  transition: border-color 0.2s ease;
}

.welcome-text h2 {
  background: linear-gradient(135deg, var(--dark-grey) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Feature Pills */
.bg-white.bg-opacity-10 {
  transition: all 0.3s ease;
}

.bg-white.bg-opacity-10:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
}

/* ===== MAIN LAYOUT STYLES ===== */

/* Main Layout Container */
.main-layout {
  background: var(--light-grey);
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* Sidebar Styles */
.main-sidebar {
  transition: all 0.3s ease;
  z-index: 1000;
}

.sidebar-header {
  background: linear-gradient(135deg, var(--white) 0%, var(--light-grey) 100%);
  border-bottom: 1px solid #e2e8f0 !important;
  min-height: 80px;
  display: flex;
  align-items: center;
}

.sidebar-header h5 {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.sidebar-footer {
  background: var(--light-grey);
  border-top: 1px solid #e2e8f0 !important;
  min-height: 80px;
  display: flex;
  align-items: center;
}

/* Collapsed Sidebar Specific Styles */
.ps-sidebar-container.ps-collapsed .sidebar-header,
.ps-sidebar-container.ps-collapsed .sidebar-footer {
  padding: 1rem 0.5rem !important;
}

.ps-sidebar-container.ps-collapsed .ps-menu-button {
  justify-content: center !important;
  padding: 12px 8px !important;
  margin: 4px 8px !important;
}

.ps-sidebar-container.ps-collapsed .ps-menu-icon {
  margin-right: 0 !important;
  min-width: auto !important;
}

.ps-sidebar-container.ps-collapsed .ps-menu-label {
  display: none !important;
}

/* SubMenu Styles for Collapsed State */
.ps-sidebar-container.ps-collapsed .ps-submenu-root > .ps-menu-button {
  position: relative;
}

.ps-sidebar-container.ps-collapsed .ps-submenu-root > .ps-menu-button::after {
  content: '';
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 4px;
  background: var(--neutral-grey);
  border-radius: 50%;
}

/* Tooltip for collapsed items */
.ps-sidebar-container.ps-collapsed .ps-menu-button {
  position: relative;
}

.ps-sidebar-container.ps-collapsed .ps-menu-button:hover::before {
  content: attr(title);
  position: absolute;
  left: 100%;
  top: 50%;
  transform: translateY(-50%);
  background: var(--dark-grey);
  color: white;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
  font-size: 0.875rem;
  white-space: nowrap;
  z-index: 1000;
  margin-left: 0.5rem;
  opacity: 0;
  animation: tooltipFadeIn 0.3s ease forwards;
}

@keyframes tooltipFadeIn {
  from { opacity: 0; transform: translateY(-50%) translateX(-10px); }
  to { opacity: 1; transform: translateY(-50%) translateX(0); }
}

.user-avatar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* Top Header Styles */
.top-header {
  background: var(--white) !important;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
  border-bottom: 1px solid #e2e8f0 !important;
  z-index: 999;
}

.top-header .btn-link {
  border: none !important;
  text-decoration: none !important;
  transition: all 0.3s ease;
}

.top-header .btn-link:hover {
  color: var(--primary-blue) !important;
  transform: translateY(-1px);
}

/* Search Bar Styles */
.search-container .form-control {
  border: 2px solid #e2e8f0;
  background: var(--light-grey);
  transition: all 0.3s ease;
}

.search-container .form-control:focus,
.search-container .form-control.search-focused {
  border-color: var(--secondary-blue);
  background: var(--white);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.search-container .search-icon {
  transition: color 0.3s ease;
}

.search-container .form-control:focus + .search-icon {
  color: var(--secondary-blue) !important;
}

.search-container .search-clear {
  transition: all 0.3s ease;
}

.search-container .search-clear:hover {
  color: var(--primary-blue) !important;
  transform: translateY(-50%) scale(1.1);
}

/* Breadcrumb Styles */
.breadcrumb {
  background: none;
  padding: 0;
}

.breadcrumb-item a {
  color: var(--neutral-grey);
  transition: color 0.3s ease;
}

.breadcrumb-item a:hover {
  color: var(--primary-blue);
}

.breadcrumb-item.active {
  color: var(--dark-grey);
  font-weight: 600;
}

/* Notification Badge */
.badge.bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

/* Dropdown Menu Styles */
.dropdown-menu {
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.dropdown-header {
  color: var(--dark-grey);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.5rem 1rem;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
}

.dropdown-item:hover {
  background: var(--light-grey);
  color: var(--primary-blue);
  transform: translateX(4px);
}

.dropdown-item.text-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* Notification Menu Styles */
.notification-menu {
  max-height: 400px;
  overflow-y: auto;
}

.notification-menu .dropdown-item {
  border-radius: 0;
  margin: 0;
  border-bottom: 1px solid #f1f5f9;
}

.notification-menu .dropdown-item:last-child {
  border-bottom: none;
}

.notification-menu .dropdown-item:hover {
  background: var(--light-grey);
  transform: none;
}

/* User Menu Enhanced Styles */
.dropdown-menu .dropdown-header {
  background: var(--light-grey);
  margin: 0 -0.5rem;
  padding: 1rem;
  border-radius: 12px 12px 0 0;
}

/* Page Content Styles */
.page-content {
  background: var(--light-grey);
  min-height: calc(100vh - 80px);
}

/* Responsive Sidebar */
@media (max-width: 991.98px) {
  .main-sidebar {
    position: fixed !important;
    height: 100vh;
    z-index: 1050;
  }

  .main-content {
    margin-left: 0 !important;
  }
}

/* Sidebar Menu Item Animations */
.ps-menu-button {
  position: relative;
  overflow: hidden;
}

.ps-menu-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s;
}

.ps-menu-button:hover::before {
  left: 100%;
}

/* Professional Loading States */
.main-layout .loading {
  position: relative;
}

.main-layout .loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Enhanced Focus States for Accessibility */
.main-layout button:focus,
.main-layout .form-control:focus,
.main-layout .dropdown-toggle:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Professional Scrollbar */
.main-layout ::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.main-layout ::-webkit-scrollbar-track {
  background: var(--light-grey);
}

.main-layout ::-webkit-scrollbar-thumb {
  background: var(--neutral-grey);
  border-radius: 3px;
}

.main-layout ::-webkit-scrollbar-thumb:hover {
  background: var(--dark-grey);
}

/* Print Styles */
@media print {
  .auth-left-panel {
    display: none;
  }

  .auth-right-panel {
    width: 100% !important;
  }

  .auth-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .main-sidebar,
  .top-header {
    display: none !important;
  }

  .main-content {
    margin: 0 !important;
    padding: 0 !important;
  }

  .page-content {
    padding: 1rem !important;
  }
}

/* ===== DASHBOARD HOME STYLES ===== */

/* Dashboard Cards */
.dashboard-home .card {
  transition: all 0.3s ease;
  border-radius: 12px;
}

.dashboard-home .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Stats Cards */
.dashboard-home .card-body {
  padding: 1.5rem;
}

.dashboard-home .h4 {
  font-size: 2rem;
  font-weight: 700;
}

/* Table Styles */
.dashboard-home .table {
  font-size: 0.9rem;
}

.dashboard-home .table th {
  font-weight: 600;
  color: var(--dark-grey);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 1.5rem;
}

.dashboard-home .table td {
  padding: 1rem 1.5rem;
  vertical-align: middle;
}

.dashboard-home .table-hover tbody tr:hover {
  background-color: var(--light-grey);
}

/* Progress Bar */
.dashboard-home .progress {
  background-color: #e9ecef;
  border-radius: 10px;
}

.dashboard-home .progress-bar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border-radius: 10px;
}

/* Badges */
.dashboard-home .badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

/* Quick Actions Buttons */
.dashboard-home .btn-outline-primary,
.dashboard-home .btn-outline-success,
.dashboard-home .btn-outline-info,
.dashboard-home .btn-outline-warning {
  border-width: 2px;
  font-weight: 500;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.dashboard-home .btn-outline-primary:hover {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border-color: var(--primary-blue);
  transform: translateX(4px);
}

/* Card Headers */
.dashboard-home .card-header {
  background: var(--white) !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 1.25rem 1.5rem;
}

.dashboard-home .card-header h5 {
  color: var(--dark-grey);
  font-weight: 600;
}

/* Welcome Header */
.dashboard-home h1 {
  background: linear-gradient(135deg, var(--dark-grey) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Action Buttons in Header */
.dashboard-home .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dashboard-home .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.dashboard-home .btn-outline-primary {
  border-color: var(--secondary-blue);
  color: var(--secondary-blue);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-home .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }

  .dashboard-home .d-flex.gap-2 {
    justify-content: stretch;
  }

  .dashboard-home .btn {
    flex: 1;
  }

  .dashboard-home .table-responsive {
    font-size: 0.85rem;
  }

  .dashboard-home .card-body {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .dashboard-home .col-xl-3 {
    margin-bottom: 1rem;
  }

  .dashboard-home .h4 {
    font-size: 1.5rem;
  }

  .dashboard-home .card-header {
    padding: 1rem;
  }

  .dashboard-home .table th,
  .dashboard-home .table td {
    padding: 0.75rem 1rem;
  }
}

/* ===== FORGOT PASSWORD STYLES ===== */

/* Forgot Password Container */
.forgot-password-container {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Forgot Password Form */
.forgot-password-form {
  max-width: 100%;
}

.forgot-password-form .form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.forgot-password-form .form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.forgot-password-form .form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.forgot-password-form .form-control.ps-5 {
  padding-left: 3rem !important;
}

.forgot-password-form .position-relative .position-absolute {
  pointer-events: none;
}

/* Icons */
.forgot-password-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.email-sent-icon {
  animation: successBounce 0.6s ease-out;
}

@keyframes successBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* Email Display */
.email-display {
  background: linear-gradient(135deg, var(--light-grey) 0%, #e2e8f0 100%) !important;
  border: 2px solid var(--secondary-blue);
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

/* Instructions Card */
.instructions-card {
  background: linear-gradient(135deg, var(--light-grey) 0%, #f8fafc 100%) !important;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.instructions-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.instruction-steps .badge {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Buttons */
.forgot-password-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.forgot-password-form .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.forgot-password-form .btn-primary:hover::before {
  left: 100%;
}

.forgot-password-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.forgot-password-form .btn-primary:active {
  transform: translateY(0);
}

.forgot-password-form .btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.forgot-password-form .btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

.forgot-password-form .btn-outline-primary {
  border: 2px solid var(--secondary-blue);
  color: var(--secondary-blue);
  background: transparent;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.forgot-password-form .btn-outline-primary:hover {
  background: var(--secondary-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.forgot-password-form .btn-outline-primary:disabled {
  border-color: #94a3b8;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

/* Links */
.forgot-password-form a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.forgot-password-form a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
  transform: translateX(-2px);
}

/* Validation Styles */
.forgot-password-form .is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.forgot-password-form .invalid-feedback {
  display: block;
  font-size: 0.875rem;
  color: #dc3545;
  margin-top: 0.5rem;
}

/* Resend Section */
.resend-section {
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Loading Animation */
.forgot-password-form .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
  .forgot-password-form {
    padding: 1rem;
  }

  .forgot-password-icon {
    margin-bottom: 1rem;
  }

  .email-sent-icon {
    margin-bottom: 1rem;
  }

  .instructions-card {
    padding: 1.5rem !important;
  }

  .instruction-steps .badge {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .forgot-password-form h2 {
    font-size: 1.5rem;
  }

  .forgot-password-form .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .email-display {
    padding: 1rem !important;
    font-size: 0.9rem;
  }
}
/* TriTrackz Logistics Theme - Professional Login Layout */

:root {
  /* Logistics Color Palette */
  --primary-blue: #1e40af;
  --secondary-blue: #3b82f6;
  --accent-orange: #f97316;
  --neutral-grey: #64748b;
  --light-grey: #f1f5f9;
  --dark-grey: #334155;
  --success-green: #10b981;
  --white: #ffffff;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-heavy: rgba(0, 0, 0, 0.25);
}

/* Global Overrides */
.bg-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
}

/* Auth Layout Specific Styles */
.auth-layout {
  min-height: 100vh;
  background: var(--light-grey);
}

.auth-left-panel {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  position: relative;
  overflow: hidden;
}

.auth-left-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.auth-brand-container {
  position: relative;
  z-index: 2;
}

.auth-brand-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.auth-brand-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-weight: 300;
}

.logistics-icons {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.logistics-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: iconFloat 3s ease-in-out infinite;
}

.logistics-icon:nth-child(2) {
  animation-delay: 0.5s;
}

.logistics-icon:nth-child(3) {
  animation-delay: 1s;
}

.logistics-icon:nth-child(4) {
  animation-delay: 1.5s;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

.logistics-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px) scale(1.1);
}

.logistics-icon svg {
  width: 28px;
  height: 28px;
  color: var(--white);
}

/* Right Panel Styles */
.auth-right-panel {
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  transition: all 0.3s ease;
}

.auth-card:hover {
  box-shadow: 0 25px 50px -12px var(--shadow-medium);
  transform: translateY(-2px);
}

/* Form Styles */
.login-form .form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.login-form .form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.login-form .form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
}

/* Input with Icons Styling */
.login-form .form-control.ps-5 {
  padding-left: 3rem !important;
}

.login-form .form-control.pe-5 {
  padding-right: 3rem !important;
}

.login-form .position-relative .position-absolute {
  pointer-events: none;
}

.login-form .position-relative button.position-absolute {
  pointer-events: auto;
  z-index: 10;
}

.login-form .position-relative button.position-absolute:hover {
  color: var(--secondary-blue) !important;
}

.login-form .position-relative button.position-absolute:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
  border-radius: 4px;
}

.login-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-form .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-form .btn-primary:hover::before {
  left: 100%;
}

.login-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.login-form .btn-primary:active {
  transform: translateY(0);
}

/* Welcome Text Styles */
.welcome-text h2 {
  color: var(--dark-grey);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.welcome-text p {
  color: var(--neutral-grey);
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* Links and Interactive Elements */
.login-form a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.login-form a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}

.form-check-input:checked {
  background-color: var(--secondary-blue);
  border-color: var(--secondary-blue);
}

.form-check-input:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .auth-brand-title {
    font-size: 2.5rem;
  }

  .auth-brand-subtitle {
    font-size: 1rem;
  }

  .logistics-icons {
    gap: 1rem;
    margin-top: 2rem;
  }

  .logistics-icon {
    width: 50px;
    height: 50px;
  }

  .logistics-icon svg {
    width: 24px;
    height: 24px;
  }

  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }

  .welcome-text h2 {
    font-size: 1.75rem;
  }
}

@media (max-width: 576px) {
  .auth-brand-title {
    font-size: 2rem;
  }

  .auth-card {
    padding: 1.5rem;
  }

  .logistics-icons {
    gap: 0.75rem;
  }

  .logistics-icon {
    width: 45px;
    height: 45px;
  }
}

/* Loading Animation */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

/* Accessibility Improvements */
.auth-card:focus-within {
  box-shadow: 0 25px 50px -12px var(--shadow-medium), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Additional Professional Enhancements */
.auth-layout {
  animation: fadeIn 0.8s ease-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.auth-card {
  animation: slideUp 0.6s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.auth-brand-container {
  animation: slideInLeft 0.8s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Enhanced Input Focus States */
.login-form .form-control:focus {
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Professional Loading State */
.login-form .btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.login-form .btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Enhanced Mobile Experience */
@media (max-width: 768px) {
  .auth-layout {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  }

  .auth-right-panel {
    background: transparent;
    padding: 1rem;
  }

  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
}

/* Improved Accessibility */
.logistics-icon:focus {
  outline: 2px solid rgba(255, 255, 255, 0.8);
  outline-offset: 2px;
}

.login-form .form-control:focus {
  outline: none;
}

.login-form .btn-primary:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* Professional Hover Effects */
.auth-card .form-control:hover {
  border-color: #cbd5e1;
  transition: border-color 0.2s ease;
}

.welcome-text h2 {
  background: linear-gradient(135deg, var(--dark-grey) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Enhanced Feature Pills */
.bg-white.bg-opacity-10 {
  transition: all 0.3s ease;
}

.bg-white.bg-opacity-10:hover {
  background: rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-2px);
}

/* Print Styles */
@media print {
  .auth-left-panel {
    display: none;
  }

  .auth-right-panel {
    width: 100% !important;
  }

  .auth-card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}
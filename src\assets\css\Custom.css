/* TriTrackz Logistics Theme - Common Styles Only */

/* ===== CSS VARIABLES ===== */
:root {
  /* Logistics Color Palette */
  --primary-blue: #1e40af;
  --secondary-blue: #3b82f6;
  --accent-orange: #f97316;
  --neutral-grey: #64748b;
  --light-grey: #f1f5f9;
  --dark-grey: #334155;
  --success-green: #10b981;
  --white: #ffffff;
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-heavy: rgba(0, 0, 0, 0.25);
}

/* ===== GLOBAL OVERRIDES ===== */
.bg-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
}

/* ===== COMMON ANIMATIONS ===== */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes iconPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

@keyframes successBounce {
  0% { transform: scale(0); }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); }
}

/* ===== COMMON FORM STYLES ===== */
.form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.form-control.ps-5 {
  padding-left: 3rem !important;
}

.form-control.pe-5 {
  padding-right: 3rem !important;
}

.position-relative .position-absolute {
  pointer-events: none;
}

.position-relative button.position-absolute {
  pointer-events: auto;
  z-index: 10;
}

.form-check-input:checked {
  background-color: var(--secondary-blue);
  border-color: var(--secondary-blue);
}

.form-check-input:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* ===== COMMON BUTTON STYLES ===== */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

.btn-outline-primary {
  border: 2px solid var(--secondary-blue);
  color: var(--secondary-blue);
  background: transparent;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.btn-outline-primary:hover {
  background: var(--secondary-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.btn-outline-primary:disabled {
  border-color: #94a3b8;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

.btn-link {
  border: none !important;
  text-decoration: none !important;
  transition: all 0.3s ease;
}

.btn-link:hover {
  color: var(--primary-blue) !important;
  transform: translateY(-1px);
}

/* ===== COMMON LINK STYLES ===== */
a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}

/* ===== COMMON CARD STYLES ===== */
.card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
}

.card:hover {
  box-shadow: 0 25px 50px -12px var(--shadow-medium);
  transform: translateY(-2px);
}

/* ===== COMMON DROPDOWN STYLES ===== */
.dropdown-menu {
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  border-radius: 12px;
  padding: 0.5rem 0;
  margin-top: 0.5rem;
}

.dropdown-header {
  color: var(--dark-grey);
  font-weight: 600;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 0.5rem 1rem;
}

.dropdown-item {
  padding: 0.75rem 1rem;
  font-size: 0.875rem;
  transition: all 0.3s ease;
  border-radius: 8px;
  margin: 0 0.5rem;
}

.dropdown-item:hover {
  background: var(--light-grey);
  color: var(--primary-blue);
  transform: translateX(4px);
}

.dropdown-item.text-danger:hover {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

/* ===== COMMON BADGE STYLES ===== */
.badge.bg-danger {
  background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%) !important;
  box-shadow: 0 2px 4px rgba(239, 68, 68, 0.3);
}

.badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

/* ===== COMMON USER AVATAR STYLES ===== */
.user-avatar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%) !important;
  box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

/* ===== COMMON LOADING STYLES ===== */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

.loading {
  position: relative;
}

.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.8), transparent);
  animation: shimmer 1.5s infinite;
}

/* ===== COMMON ACCESSIBILITY STYLES ===== */
button:focus,
.form-control:focus,
.dropdown-toggle:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
}

/* ===== COMMON SCROLLBAR STYLES ===== */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--light-grey);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-grey);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-grey);
}

/* ===== COMMON RESPONSIVE UTILITIES ===== */
@media (max-width: 768px) {
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }

  .card {
    padding: 1rem;
    margin: 1rem;
  }
}

@media (max-width: 576px) {
  .btn {
    padding: 0.75rem 1rem;
    font-size: 0.85rem;
  }

  .card {
    padding: 1rem;
  }
}

/* ===== PRINT STYLES ===== */
@media print {
  .btn,
  .dropdown,
  .navbar,
  .sidebar {
    display: none !important;
  }

  .card {
    box-shadow: none;
    border: 1px solid #ccc;
  }
}

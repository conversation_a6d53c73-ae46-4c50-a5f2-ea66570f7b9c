import React, { useState } from "react";
import { FaSearch, FaTimes } from "react-icons/fa";

const SearchBar = ({ placeholder = "Search shipments, customers...", onSearch }) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [isFocused, setIsFocused] = useState(false);

  const handleSearch = (e) => {
    e.preventDefault();
    if (onSearch) {
      onSearch(searchTerm);
    }
  };

  const handleClear = () => {
    setSearchTerm("");
    if (onSearch) {
      onSearch("");
    }
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      handleSearch(e);
    }
  };

  return (
    <div className="search-container d-none d-md-block">
      <form onSubmit={handleSearch} className="position-relative">
        <input
          type="text"
          className={`form-control ps-5 ${searchTerm ? 'pe-5' : ''} ${isFocused ? 'search-focused' : ''}`}
          placeholder={placeholder}
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
          onKeyPress={handleKeyPress}
          style={{ 
            width: '300px', 
            borderRadius: '25px',
            transition: 'all 0.3s ease'
          }}
        />
        <FaSearch
          className="position-absolute text-muted search-icon"
          style={{
            left: '1rem',
            top: '50%',
            transform: 'translateY(-50%)',
            fontSize: '0.875rem',
            pointerEvents: 'none'
          }}
        />
        {searchTerm && (
          <button
            type="button"
            className="btn btn-link position-absolute text-muted p-0 search-clear"
            onClick={handleClear}
            style={{
              right: '1rem',
              top: '50%',
              transform: 'translateY(-50%)',
              border: 'none',
              background: 'none',
              fontSize: '0.875rem'
            }}
            title="Clear search"
          >
            <FaTimes size={14} />
          </button>
        )}
      </form>
    </div>
  );
};

export default SearchBar;

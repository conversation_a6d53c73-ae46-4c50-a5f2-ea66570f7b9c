import React from "react";
import { Link, useLocation } from "react-router-dom";
import ROUTES from "@constants/routes";

const Breadcrumb = () => {
  const location = useLocation();
  
  // Generate breadcrumb items based on current path
  const generateBreadcrumbs = () => {
    const pathSegments = location.pathname.split('/').filter(segment => segment);
    const breadcrumbs = [
      { label: 'Dashboard', path: ROUTES.HOME, active: false }
    ];

    if (pathSegments.length === 0 || location.pathname === ROUTES.HOME) {
      breadcrumbs[0].active = true;
      return breadcrumbs;
    }

    // Add more breadcrumb logic here based on your routes
    pathSegments.forEach((segment, index) => {
      const path = '/' + pathSegments.slice(0, index + 1).join('/');
      const isActive = index === pathSegments.length - 1;
      
      // Map segments to readable labels
      const labelMap = {
        'fleet': 'Fleet Management',
        'vehicles': 'Vehicles',
        'drivers': 'Drivers',
        'routes': 'Routes',
        'shipments': 'Shipments',
        'track': 'Track Shipments',
        'invoices': 'Invoices',
        'warehouses': 'Warehouses',
        'analytics': 'Analytics',
        'customers': 'Customers',
        'settings': 'Settings'
      };

      breadcrumbs.push({
        label: labelMap[segment] || segment.charAt(0).toUpperCase() + segment.slice(1),
        path: path,
        active: isActive
      });
    });

    return breadcrumbs;
  };

  const breadcrumbs = generateBreadcrumbs();

  return (
    <nav aria-label="breadcrumb">
      <ol className="breadcrumb mb-0">
        {breadcrumbs.map((crumb, index) => (
          <li 
            key={index} 
            className={`breadcrumb-item ${crumb.active ? 'active' : ''}`}
            aria-current={crumb.active ? 'page' : undefined}
          >
            {crumb.active ? (
              crumb.label
            ) : (
              <Link to={crumb.path} className="text-decoration-none">
                {crumb.label}
              </Link>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;

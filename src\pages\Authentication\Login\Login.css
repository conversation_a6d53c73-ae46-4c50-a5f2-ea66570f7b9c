/* Login Component Styles */

/* Welcome Text Styles */
.welcome-text h2 {
  color: var(--dark-grey);
  font-weight: 700;
  margin-bottom: 0.5rem;
  font-size: 2rem;
  background: linear-gradient(135deg, var(--dark-grey) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-text p {
  color: var(--neutral-grey);
  font-size: 1rem;
  margin-bottom: 2rem;
}

/* Login Form Styles */
.login-form .form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.login-form .form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.login-form .form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Input with Icons Styling */
.login-form .form-control.ps-5 {
  padding-left: 3rem !important;
}

.login-form .form-control.pe-5 {
  padding-right: 3rem !important;
}

.login-form .position-relative .position-absolute {
  pointer-events: none;
}

.login-form .position-relative button.position-absolute {
  pointer-events: auto;
  z-index: 10;
}

.login-form .position-relative button.position-absolute:hover {
  color: var(--secondary-blue) !important;
}

.login-form .position-relative button.position-absolute:focus {
  outline: 2px solid rgba(59, 130, 246, 0.5);
  outline-offset: 2px;
  border-radius: 4px;
}

/* Button Styles */
.login-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.login-form .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-form .btn-primary:hover::before {
  left: 100%;
}

.login-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.login-form .btn-primary:active {
  transform: translateY(0);
}

.login-form .btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.login-form .btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

/* Links and Interactive Elements */
.login-form a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.login-form a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
}

.form-check-input:checked {
  background-color: var(--secondary-blue);
  border-color: var(--secondary-blue);
}

.form-check-input:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 0.25rem rgba(59, 130, 246, 0.25);
}

/* Responsive Design */
@media (max-width: 768px) {
  .welcome-text h2 {
    font-size: 1.75rem;
  }
}

/* Loading Animation */
.spinner-border-sm {
  width: 1rem;
  height: 1rem;
}

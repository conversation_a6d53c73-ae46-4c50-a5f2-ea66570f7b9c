import React, { useState } from "react";
import { Sidebar, Menu, MenuItem, SubMenu } from "react-pro-sidebar";
import { Link, Outlet, useLocation, useNavigate } from "react-router-dom";
import { useSelector, useDispatch } from "react-redux";
import {
  FaHome,
  FaTruck,
  FaBoxOpen,
  FaRoute,
  FaWarehouse,
  FaChartBar,
  FaUsers,
  FaCog,
  FaBars,
  FaTimes,
  FaBell,
  FaSearch,
  FaUser,
  FaSignOutAlt,
  FaMapMarkedAlt,
  FaClipboardList,
  FaFileInvoiceDollar,
  FaUserTie,
} from "react-icons/fa";
import { resetUser } from "@store/userSlice";
import { TriTrackzLogo } from "@components/LogisticsIcons";
import ROUTES from "@constants/routes";
import toast from "react-hot-toast";

const MainLayout = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [toggled, setToggled] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const user = useSelector((state) => state.user.user);

  const handleLogout = () => {
    dispatch(resetUser());
    toast.success("Logged out successfully");
    navigate(ROUTES.LOGIN);
  };

  const toggleSidebar = () => {
    setCollapsed(!collapsed);
  };

  const toggleMobileSidebar = () => {
    setToggled(!toggled);
  };

  return (
    <div className="main-layout d-flex vh-100">
      {/* Professional Sidebar */}
      <Sidebar
        className="main-sidebar"
        breakPoint="lg"
        width="280px"
        collapsedWidth="80px"
        collapsed={collapsed}
        toggled={toggled}
        onBackdropClick={() => setToggled(false)}
        backgroundColor="var(--white)"
        rootStyles={{
          border: "none",
          boxShadow: "2px 0 10px rgba(0, 0, 0, 0.1)",
        }}
      >
        <div className="sidebar-content h-100 d-flex flex-column">
          {/* Logo Section */}
          <div className="sidebar-header p-3 border-bottom">
            <div className="d-flex align-items-center justify-content-center">
              {!collapsed ? (
                <div className="d-flex align-items-center">
                  <TriTrackzLogo size={40} className="me-2" />
                  <div>
                    <h5 className="mb-0 text-primary fw-bold">TRITRACKZ</h5>
                    <small className="text-muted">Logistics Hub</small>
                  </div>
                </div>
              ) : (
                <TriTrackzLogo size={40} />
              )}
            </div>
          </div>

          {/* Navigation Menu */}
          <Menu
            className="flex-grow-1"
            menuItemStyles={{
              button: {
                padding: "12px 20px",
                margin: "4px 12px",
                borderRadius: "8px",
                color: "var(--neutral-grey)",
                fontSize: "0.95rem",
                fontWeight: "500",
                transition: "all 0.3s ease",
                "&:hover": {
                  backgroundColor: "var(--light-grey)",
                  color: "var(--primary-blue)",
                },
                "&.ps-active": {
                  backgroundColor: "var(--secondary-blue)",
                  color: "white",
                  boxShadow: "0 4px 12px rgba(59, 130, 246, 0.3)",
                },
              },
              icon: {
                marginRight: collapsed ? "0" : "12px",
              },
            }}
          >
            <MenuItem
              icon={<FaHome size={18} />}
              active={location.pathname === ROUTES.HOME}
              component={<Link to={ROUTES.HOME} />}
            >
              Dashboard
            </MenuItem>

            <SubMenu icon={<FaTruck size={18} />} label="Fleet Management">
              <MenuItem icon={<FaTruck size={16} />}>Vehicles</MenuItem>
              <MenuItem icon={<FaUserTie size={16} />}>Drivers</MenuItem>
              <MenuItem icon={<FaRoute size={16} />}>Routes</MenuItem>
            </SubMenu>

            <SubMenu icon={<FaBoxOpen size={18} />} label="Shipments">
              <MenuItem icon={<FaClipboardList size={16} />}>
                All Shipments
              </MenuItem>
              <MenuItem icon={<FaMapMarkedAlt size={16} />}>
                Track Shipments
              </MenuItem>
              <MenuItem icon={<FaFileInvoiceDollar size={16} />}>
                Invoices
              </MenuItem>
            </SubMenu>

            <MenuItem icon={<FaWarehouse size={18} />}>Warehouses</MenuItem>

            <MenuItem icon={<FaChartBar size={18} />}>Analytics</MenuItem>

            <MenuItem icon={<FaUsers size={18} />}>Customers</MenuItem>

            <MenuItem icon={<FaCog size={18} />}>Settings</MenuItem>
          </Menu>

          {/* User Section */}
          {!collapsed && (
            <div className="sidebar-footer p-3 border-top">
              <div className="d-flex align-items-center">
                <div
                  className="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-3"
                  style={{ width: "40px", height: "40px" }}
                >
                  <FaUser size={16} />
                </div>
                <div className="flex-grow-1">
                  <div className="fw-semibold text-dark">
                    {user?.name || "User"}
                  </div>
                  <small className="text-muted">
                    {user?.email || "<EMAIL>"}
                  </small>
                </div>
              </div>
            </div>
          )}
        </div>
      </Sidebar>

      {/* Main Content Area */}
      <div className="main-content flex-grow-1 d-flex flex-column overflow-hidden">
        {/* Top Header Bar */}
        <header className="top-header bg-white border-bottom px-4 py-3">
          <div className="d-flex align-items-center justify-content-between">
            {/* Left Section - Menu Toggle & Breadcrumb */}
            <div className="d-flex align-items-center">
              <button
                className="btn btn-link text-muted p-0 me-3 d-lg-none"
                onClick={toggleMobileSidebar}
              >
                <FaBars size={20} />
              </button>
              <button
                className="btn btn-link text-muted p-0 me-3 d-none d-lg-block"
                onClick={toggleSidebar}
              >
                {collapsed ? <FaBars size={20} /> : <FaTimes size={20} />}
              </button>
              <nav aria-label="breadcrumb">
                <ol className="breadcrumb mb-0">
                  <li className="breadcrumb-item">
                    <Link to={ROUTES.HOME} className="text-decoration-none">
                      Dashboard
                    </Link>
                  </li>
                  <li className="breadcrumb-item active" aria-current="page">
                    Home
                  </li>
                </ol>
              </nav>
            </div>

            {/* Center Section - Search */}
            <div className="search-container d-none d-md-block">
              <div className="position-relative">
                <input
                  type="text"
                  className="form-control ps-5"
                  placeholder="Search shipments, customers..."
                  style={{ width: "300px", borderRadius: "25px" }}
                />
                <FaSearch
                  className="position-absolute text-muted"
                  style={{
                    left: "1rem",
                    top: "50%",
                    transform: "translateY(-50%)",
                    fontSize: "0.875rem",
                  }}
                />
              </div>
            </div>

            {/* Right Section - Notifications & User Menu */}
            <div className="d-flex align-items-center gap-3">
              <button className="btn btn-link text-muted p-0 position-relative">
                <FaBell size={18} />
                <span
                  className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger"
                  style={{ fontSize: "0.6rem" }}
                >
                  3
                </span>
              </button>

              <div className="dropdown">
                <button
                  className="btn btn-link text-muted p-0 d-flex align-items-center"
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  data-bs-toggle="dropdown"
                >
                  <div
                    className="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2"
                    style={{ width: "32px", height: "32px" }}
                  >
                    <FaUser size={14} />
                  </div>
                  <span className="d-none d-md-inline fw-semibold">
                    {user?.name || "User"}
                  </span>
                </button>
                <ul className="dropdown-menu dropdown-menu-end">
                  <li>
                    <h6 className="dropdown-header">Account</h6>
                  </li>
                  <li>
                    <a className="dropdown-item" href="#">
                      <FaUser className="me-2" size={14} />
                      Profile
                    </a>
                  </li>
                  <li>
                    <a className="dropdown-item" href="#">
                      <FaCog className="me-2" size={14} />
                      Settings
                    </a>
                  </li>
                  <li>
                    <hr className="dropdown-divider" />
                  </li>
                  <li>
                    <button
                      className="dropdown-item text-danger"
                      onClick={handleLogout}
                    >
                      <FaSignOutAlt className="me-2" size={14} />
                      Logout
                    </button>
                  </li>
                </ul>
              </div>
            </div>
          </div>
        </header>

        {/* Page Content */}
        <main className="page-content flex-grow-1 overflow-auto p-4">
          <Outlet />
        </main>
      </div>
    </div>
  );
};

export default MainLayout;

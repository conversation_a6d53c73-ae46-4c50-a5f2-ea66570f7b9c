/* AuthLayout Component Styles */

/* Auth Layout Container */
.auth-layout {
  min-height: 100vh;
  background: var(--light-grey);
  animation: fadeIn 0.8s ease-out;
}

/* Left Panel Styles */
.auth-left-panel {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  position: relative;
  overflow: hidden;
}

.auth-left-panel::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

.auth-brand-container {
  position: relative;
  z-index: 2;
  animation: slideInLeft 0.8s ease-out;
}

.auth-brand-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: var(--white);
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
  margin-bottom: 1rem;
  letter-spacing: -0.02em;
}

.auth-brand-subtitle {
  font-size: 1.25rem;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 2rem;
  font-weight: 300;
}

/* Logistics Icons */
.logistics-icons {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 3rem;
  flex-wrap: wrap;
}

.logistics-icon {
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  -webkit-backdrop-filter: blur(10px);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  animation: iconFloat 3s ease-in-out infinite;
}

.logistics-icon:nth-child(2) {
  animation-delay: 0.5s;
}

.logistics-icon:nth-child(3) {
  animation-delay: 1s;
}

.logistics-icon:nth-child(4) {
  animation-delay: 1.5s;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(0px) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

.logistics-icon:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-3px) scale(1.1);
}

.logistics-icon svg {
  width: 28px;
  height: 28px;
  color: var(--white);
}

/* Right Panel Styles */
.auth-right-panel {
  background: var(--white);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
}

.auth-card {
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 20px 25px -5px var(--shadow-light), 0 10px 10px -5px var(--shadow-light);
  border: 1px solid rgba(0, 0, 0, 0.05);
  padding: 3rem;
  width: 100%;
  max-width: 450px;
  transition: all 0.3s ease;
  animation: slideUp 0.6s ease-out;
}

.auth-card:hover {
  box-shadow: 0 25px 50px -12px var(--shadow-medium);
  transform: translateY(-2px);
}

/* Enhanced Mobile Experience */
@media (max-width: 768px) {
  .auth-layout {
    background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  }
  
  .auth-right-panel {
    background: transparent;
    padding: 1rem;
  }
  
  .auth-card {
    background: rgba(255, 255, 255, 0.95);
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .auth-brand-title {
    font-size: 2.5rem;
  }
  
  .auth-brand-subtitle {
    font-size: 1rem;
  }
  
  .logistics-icons {
    gap: 1rem;
    margin-top: 2rem;
  }
  
  .logistics-icon {
    width: 50px;
    height: 50px;
  }
  
  .logistics-icon svg {
    width: 24px;
    height: 24px;
  }
  
  .auth-card {
    padding: 2rem;
    margin: 1rem;
  }
}

@media (max-width: 576px) {
  .auth-brand-title {
    font-size: 2rem;
  }
  
  .auth-card {
    padding: 1.5rem;
  }
  
  .logistics-icons {
    gap: 0.75rem;
  }
  
  .logistics-icon {
    width: 45px;
    height: 45px;
  }
}

/* Accessibility Improvements */
.auth-card:focus-within {
  box-shadow: 0 25px 50px -12px var(--shadow-medium), 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Print Styles */
@media print {
  .auth-left-panel {
    display: none;
  }
  
  .auth-right-panel {
    width: 100% !important;
  }
}

/* ForgotPassword Component Styles */

/* Container */
.forgot-password-container {
  animation: fadeInUp 0.6s ease-out;
}

/* Form Styles */
.forgot-password-form {
  max-width: 100%;
}

.forgot-password-form .form-label {
  color: var(--dark-grey);
  font-weight: 600;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.forgot-password-form .form-control {
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 0.75rem 1rem;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: var(--white);
}

.forgot-password-form .form-control:focus {
  border-color: var(--secondary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  outline: none;
  transform: translateY(-1px);
}

.forgot-password-form .form-control.ps-5 {
  padding-left: 3rem !important;
}

.forgot-password-form .position-relative .position-absolute {
  pointer-events: none;
}

/* Icons */
.forgot-password-icon {
  animation: iconPulse 2s ease-in-out infinite;
}

.email-sent-icon {
  animation: successBounce 0.6s ease-out;
}

/* Email Display */
.email-display {
  background: linear-gradient(135deg, var(--light-grey) 0%, #e2e8f0 100%) !important;
  border: 2px solid var(--secondary-blue);
  font-family: 'Courier New', monospace;
  letter-spacing: 0.5px;
}

/* Instructions Card */
.instructions-card {
  background: linear-gradient(135deg, var(--light-grey) 0%, #f8fafc 100%) !important;
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.instructions-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.instruction-steps .badge {
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
}

/* Buttons */
.forgot-password-form .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  border-radius: 8px;
  padding: 0.875rem 2rem;
  font-weight: 600;
  font-size: 1rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.forgot-password-form .btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.forgot-password-form .btn-primary:hover::before {
  left: 100%;
}

.forgot-password-form .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(30, 64, 175, 0.3);
}

.forgot-password-form .btn-primary:active {
  transform: translateY(0);
}

.forgot-password-form .btn-primary:disabled {
  background: linear-gradient(135deg, #94a3b8 0%, #64748b 100%);
  cursor: not-allowed;
  transform: none;
}

.forgot-password-form .btn-primary:disabled:hover {
  transform: none;
  box-shadow: none;
}

.forgot-password-form .btn-outline-primary {
  border: 2px solid var(--secondary-blue);
  color: var(--secondary-blue);
  background: transparent;
  border-radius: 8px;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  transition: all 0.3s ease;
}

.forgot-password-form .btn-outline-primary:hover {
  background: var(--secondary-blue);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.forgot-password-form .btn-outline-primary:disabled {
  border-color: #94a3b8;
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

/* Links */
.forgot-password-form a {
  color: var(--secondary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: all 0.3s ease;
}

.forgot-password-form a:hover {
  color: var(--primary-blue);
  text-decoration: underline;
  transform: translateX(-2px);
}

/* Validation Styles */
.forgot-password-form .is-invalid {
  border-color: #dc3545;
  box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.forgot-password-form .invalid-feedback {
  display: block;
  font-size: 0.875rem;
  color: #dc3545;
  margin-top: 0.5rem;
}

/* Resend Section */
.resend-section {
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

/* Loading Animation */
.forgot-password-form .spinner-border-sm {
  width: 1rem;
  height: 1rem;
  animation: spin 1s linear infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
  .forgot-password-form {
    padding: 1rem;
  }
  
  .forgot-password-icon {
    margin-bottom: 1rem;
  }
  
  .email-sent-icon {
    margin-bottom: 1rem;
  }
  
  .instructions-card {
    padding: 1.5rem !important;
  }
  
  .instruction-steps .badge {
    width: 20px;
    height: 20px;
    font-size: 0.7rem;
  }
}

@media (max-width: 576px) {
  .forgot-password-form h2 {
    font-size: 1.5rem;
  }
  
  .forgot-password-form .btn {
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .email-display {
    padding: 1rem !important;
    font-size: 0.9rem;
  }
}

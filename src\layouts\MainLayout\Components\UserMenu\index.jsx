import React from "react";
import { useSelector } from "react-redux";
import {
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  FaSignOutAlt,
  FaBell,
  FaQuestionCircle,
  FaKeyboard
} from "react-icons/fa";

const UserMenu = ({ onLogout }) => {
  const user = useSelector((state) => state.user.user);

  const handleMenuItemClick = (action) => {
    switch (action) {
      case 'profile':
        // Handle profile navigation
        console.log('Navigate to profile');
        break;
      case 'settings':
        // Handle settings navigation
        console.log('Navigate to settings');
        break;
      case 'help':
        // Handle help navigation
        console.log('Navigate to help');
        break;
      case 'shortcuts':
        // Handle keyboard shortcuts modal
        console.log('Show keyboard shortcuts');
        break;
      default:
        break;
    }
  };

  return (
    <div className="d-flex align-items-center gap-3">
      {/* Notifications */}
      <div className="dropdown">
        <button 
          className="btn btn-link text-muted p-0 position-relative"
          data-bs-toggle="dropdown"
          aria-expanded="false"
          title="Notifications"
        >
          <FaBell size={18} />
          <span className="position-absolute top-0 start-100 translate-middle badge rounded-pill bg-danger" style={{ fontSize: '0.6rem' }}>
            3
          </span>
        </button>
        <ul className="dropdown-menu dropdown-menu-end notification-menu" style={{ width: '320px' }}>
          <li><h6 className="dropdown-header d-flex justify-content-between align-items-center">
            Notifications
            <small className="text-muted">3 new</small>
          </h6></li>
          <li><hr className="dropdown-divider my-1" /></li>
          <li>
            <a className="dropdown-item py-3" href="#" onClick={(e) => e.preventDefault()}>
              <div className="d-flex">
                <div className="flex-shrink-0">
                  <div className="bg-primary rounded-circle p-2">
                    <FaBell size={12} className="text-white" />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="fw-semibold">New shipment assigned</div>
                  <div className="small text-muted">Shipment TT001 has been assigned to your route</div>
                  <div className="small text-muted">2 minutes ago</div>
                </div>
              </div>
            </a>
          </li>
          <li>
            <a className="dropdown-item py-3" href="#" onClick={(e) => e.preventDefault()}>
              <div className="d-flex">
                <div className="flex-shrink-0">
                  <div className="bg-success rounded-circle p-2">
                    <FaBell size={12} className="text-white" />
                  </div>
                </div>
                <div className="flex-grow-1 ms-3">
                  <div className="fw-semibold">Delivery completed</div>
                  <div className="small text-muted">Shipment TT002 delivered successfully</div>
                  <div className="small text-muted">1 hour ago</div>
                </div>
              </div>
            </a>
          </li>
          <li><hr className="dropdown-divider my-1" /></li>
          <li>
            <a className="dropdown-item text-center py-2" href="#" onClick={(e) => e.preventDefault()}>
              View all notifications
            </a>
          </li>
        </ul>
      </div>

      {/* User Menu */}
      <div className="dropdown">
        <button
          className="btn btn-link text-muted p-0 d-flex align-items-center"
          data-bs-toggle="dropdown"
          aria-expanded="false"
          title="User Menu"
        >
          <div className="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style={{ width: '32px', height: '32px' }}>
            <FaUser size={14} />
          </div>
          <span className="d-none d-md-inline fw-semibold">{user?.name || 'User'}</span>
        </button>
        <ul className="dropdown-menu dropdown-menu-end">
          <li><h6 className="dropdown-header">
            <div className="d-flex align-items-center">
              <div className="user-avatar bg-primary text-white rounded-circle d-flex align-items-center justify-content-center me-2" style={{ width: '24px', height: '24px' }}>
                <FaUser size={12} />
              </div>
              <div>
                <div className="fw-semibold">{user?.name || 'User'}</div>
                <small className="text-muted">{user?.email || '<EMAIL>'}</small>
              </div>
            </div>
          </h6></li>
          <li><hr className="dropdown-divider" /></li>
          <li>
            <a 
              className="dropdown-item" 
              href="#" 
              onClick={(e) => {
                e.preventDefault();
                handleMenuItemClick('profile');
              }}
            >
              <FaUser className="me-2" size={14} />
              My Profile
            </a>
          </li>
          <li>
            <a 
              className="dropdown-item" 
              href="#" 
              onClick={(e) => {
                e.preventDefault();
                handleMenuItemClick('settings');
              }}
            >
              <FaCog className="me-2" size={14} />
              Account Settings
            </a>
          </li>
          <li>
            <a 
              className="dropdown-item" 
              href="#" 
              onClick={(e) => {
                e.preventDefault();
                handleMenuItemClick('shortcuts');
              }}
            >
              <FaKeyboard className="me-2" size={14} />
              Keyboard Shortcuts
            </a>
          </li>
          <li>
            <a 
              className="dropdown-item" 
              href="#" 
              onClick={(e) => {
                e.preventDefault();
                handleMenuItemClick('help');
              }}
            >
              <FaQuestionCircle className="me-2" size={14} />
              Help & Support
            </a>
          </li>
          <li><hr className="dropdown-divider" /></li>
          <li>
            <button className="dropdown-item text-danger" onClick={onLogout}>
              <FaSignOutAlt className="me-2" size={14} />
              Sign Out
            </button>
          </li>
        </ul>
      </div>
    </div>
  );
};

export default UserMenu;

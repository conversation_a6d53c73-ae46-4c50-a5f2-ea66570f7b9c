import React from "react";
import { Outlet } from "react-router-dom";

const AuthLayout = () => {
  return (
    <div className="container-fluid vh-100">
      <div className="row vh-100">
        <div className="col-md-6 d-none d-md-flex bg-primary justify-content-center align-items-center">
          <div className="text-center text-white">
            <h1 className="display-4 fw-bold">TRITRACKZ</h1>
            <p className="lead">Track your shipments with ease</p>
            <img 
              src="/logo.png" 
              alt="TriTrackz Logo" 
              className="img-fluid mt-4" 
              style={{ maxWidth: '300px' }} 
            />
          </div>
        </div>
        <div className="col-12 col-md-6 d-flex justify-content-center align-items-center">
          <div className="card shadow border-0 p-4" style={{ maxWidth: '450px', width: '100%' }}>
            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;

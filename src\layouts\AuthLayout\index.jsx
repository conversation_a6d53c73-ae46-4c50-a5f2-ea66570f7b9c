import React from "react";
import { Outlet } from "react-router-dom";
import {
  FaShippingFast,
  FaWarehouse,
  FaMapMarkedAlt
} from "react-icons/fa";
import {
  TruckIcon,
  RouteIcon,
  PackageIcon,
  TrackingIcon,
  TriTrackzLogo
} from "@components/LogisticsIcons";
import "./AuthLayout.css";

const AuthLayout = () => {
  return (
    <div className="container-fluid auth-layout">
      <div className="row vh-100">
        {/* Left Panel - Logistics Branding */}
        <div className="col-md-6 d-none d-md-flex auth-left-panel justify-content-center align-items-center">
          <div className="auth-brand-container text-center">
            {/* Main Brand */}
            <div className="mb-4">
              <h1 className="auth-brand-title">TRITRACKZ</h1>
              <p className="auth-brand-subtitle">
                Smart Logistics & Supply Chain Management
              </p>
              <div className="d-flex justify-content-center align-items-center gap-3 mb-3">
                <div className="bg-white bg-opacity-10 px-3 py-1 rounded-pill">
                  <small className="text-white fw-semibold">Real-time Tracking</small>
                </div>
                <div className="bg-white bg-opacity-10 px-3 py-1 rounded-pill">
                  <small className="text-white fw-semibold">Route Optimization</small>
                </div>
              </div>
            </div>

            {/* Logistics Icons */}
            <div className="logistics-icons">
              <div className="logistics-icon" title="Fleet Management">
                <TruckIcon size={28} />
              </div>
              <div className="logistics-icon" title="Route Planning">
                <TrackingIcon size={28} />
              </div>
              <div className="logistics-icon" title="Package Tracking">
                <PackageIcon size={28} />
              </div>
              <div className="logistics-icon" title="Delivery Routes">
                <RouteIcon size={28} />
              </div>
            </div>

            {/* Additional Features */}
            <div className="mt-4">
              <div className="row text-center">
                <div className="col-4">
                  <FaShippingFast className="text-white mb-2" size={24} />
                  <div className="small text-white-50">Fast Delivery</div>
                </div>
                <div className="col-4">
                  <FaWarehouse className="text-white mb-2" size={24} />
                  <div className="small text-white-50">Warehouse Mgmt</div>
                </div>
                <div className="col-4">
                  <FaMapMarkedAlt className="text-white mb-2" size={24} />
                  <div className="small text-white-50">Live Tracking</div>
                </div>
              </div>
            </div>

            {/* Company Logo */}
            <div className="mt-5">
              <div className="d-flex justify-content-center">
                <TriTrackzLogo size={80} />
              </div>
              <div className="text-center mt-3">
                <div className="text-white small opacity-75">Powered by TriTrackz Logistics</div>
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Login Form */}
        <div className="col-12 col-md-6 auth-right-panel">
          <div className="auth-card">
            {/* Mobile Brand Header */}
            <div className="d-md-none text-center mb-4">
              <h2 className="text-primary fw-bold mb-1">TRITRACKZ</h2>
              <p className="text-muted small">Smart Logistics Management</p>
            </div>

            <Outlet />
          </div>
        </div>
      </div>
    </div>
  );
};

export default AuthLayout;

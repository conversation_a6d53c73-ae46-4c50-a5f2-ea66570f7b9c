import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate, Link } from "react-router-dom";
import { setToken, setUser } from "@store/userSlice";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import { useFormik } from "formik";
import { FaEnvelope, FaLock, FaEye, FaEyeSlash } from "react-icons/fa";

const Login = () => {
  const [showPassword, setShowPassword] = useState(false);
  const dispatch = useDispatch();
  const navigate = useNavigate();

  const { values, handleChange, handleSubmit, isSubmitting } = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    onSubmit: async (values) => {
      try {
        // Simulate login API call
        console.log("Login attempt:", values);
        toast.success("Login successful!");

        // Mock user data - replace with actual API response
        const mockUser = { id: 1, name: "User", email: values.email };
        const mockToken = "mock-jwt-token";

        dispatch(setUser(mockUser));
        dispatch(setToken(mockToken));
        navigate(ROUTES.HOME);
      } catch (error) {
        toast.error("Login failed. Please try again.");
      }
    },
  });

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <div className="login-form">
      <div className="welcome-text text-center mb-4">
        <h2 className="fw-bold">Welcome Back</h2>
        <p className="text-muted">Please sign in to your TriTrackz account</p>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label htmlFor="email" className="form-label">
            <FaEnvelope className="me-2" size={14} />
            Email Address
          </label>
          <div className="position-relative">
            <input
              type="email"
              className="form-control ps-5"
              id="email"
              name="email"
              value={values.email}
              onChange={handleChange}
              placeholder="Enter your email address"
              required
            />
            <FaEnvelope
              className="position-absolute text-muted"
              style={{
                left: "1rem",
                top: "50%",
                transform: "translateY(-50%)",
                fontSize: "0.875rem",
              }}
            />
          </div>
        </div>

        <div className="mb-3">
          <label htmlFor="password" className="form-label">
            <FaLock className="me-2" size={14} />
            Password
          </label>
          <div className="position-relative">
            <input
              type={showPassword ? "text" : "password"}
              className="form-control ps-5 pe-5"
              id="password"
              name="password"
              value={values.password}
              onChange={handleChange}
              placeholder="Enter your password"
              required
            />
            <FaLock
              className="position-absolute text-muted"
              style={{
                left: "1rem",
                top: "50%",
                transform: "translateY(-50%)",
                fontSize: "0.875rem",
              }}
            />
            <button
              type="button"
              className="btn btn-link position-absolute text-muted p-0"
              style={{
                right: "1rem",
                top: "50%",
                transform: "translateY(-50%)",
                border: "none",
                background: "none",
              }}
              onClick={togglePasswordVisibility}
              tabIndex={-1}
            >
              {showPassword ? <FaEyeSlash size={16} /> : <FaEye size={16} />}
            </button>
          </div>
        </div>

        <div className="d-flex justify-content-between mb-4">
          <div className="form-check">
            <input
              className="form-check-input"
              type="checkbox"
              id="rememberMe"
            />
            <label className="form-check-label" htmlFor="rememberMe">
              Remember me
            </label>
          </div>
          <Link to={ROUTES.FORGOT_PASSWORD} className="text-decoration-none">
            Forgot Password?
          </Link>
        </div>

        <button
          type="submit"
          className="btn btn-primary w-100 py-2"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <span
              className="spinner-border spinner-border-sm me-2"
              role="status"
              aria-hidden="true"
            ></span>
          ) : null}
          Sign In
        </button>
      </form>
    </div>
  );
};

export default Login;

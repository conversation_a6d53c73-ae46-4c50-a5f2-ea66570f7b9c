import React, { useState } from "react";
import { useDispatch } from "react-redux";
import { useNavigate } from "react-router-dom";
import { setToken, setUser } from "@store/userSlice";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import { useFormik } from "formik";

const Login = () => {
  const { values, handleChange, handleSubmit, isSubmitting } = useFormik({
    initialValues: {
      email: "",
      password: "",
    },
    onSubmit: (values) => {},
  });

  return (
    <div className="login-form">
      <div className="text-center mb-4">
        <h2 className="fw-bold">Welcome Back</h2>
        <p className="text-muted">Please sign in to continue</p>
      </div>

      <form onSubmit={handleSubmit}>
        <div className="mb-3">
          <label htmlFor="email" className="form-label">
            Email
          </label>
          <input
            type="email"
            className="form-control"
            id="email"
            name="email"
            value={values.email}
            onChange={handleChange}
            placeholder="Enter your email"
            required
          />
        </div>

        <div className="mb-3">
          <label htmlFor="password" className="form-label">
            Password
          </label>
          <input
            type="password"
            className="form-control"
            id="password"
            name="password"
            value={values.password}
            onChange={handleChange}
            placeholder="Enter your password"
            required
          />
        </div>

        <div className="d-flex justify-content-between mb-4">
          <div className="form-check">
            <input
              className="form-check-input"
              type="checkbox"
              id="rememberMe"
            />
            <label className="form-check-label" htmlFor="rememberMe">
              Remember me
            </label>
          </div>
          <a href="#" className="text-decoration-none">
            Forgot Password?
          </a>
        </div>

        <button
          type="submit"
          className="btn btn-primary w-100 py-2"
          disabled={isSubmitting}
        >
          {isSubmitting ? (
            <span
              className="spinner-border spinner-border-sm me-2"
              role="status"
              aria-hidden="true"
            ></span>
          ) : null}
          Sign In
        </button>
      </form>
    </div>
  );
};

export default Login;

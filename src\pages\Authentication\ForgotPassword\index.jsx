import React, { useState } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useFormik } from "formik";
import * as Yup from "yup";
import toast from "react-hot-toast";
import ROUTES from "@constants/routes";
import {
  FaEnvelope,
  FaArrowLeft,
  FaCheckCircle,
  FaSpinner,
  FaPaperPlane,
  FaShieldAlt,
  FaClock,
  FaRedo
} from "react-icons/fa";
import "./ForgotPassword.css";

const ForgotPassword = () => {
  const [step, setStep] = useState(1); // 1: Email Input, 2: <PERSON>ail Sent, 3: Success
  const [isLoading, setIsLoading] = useState(false);
  const [emailSent, setEmailSent] = useState(false);
  const [countdown, setCountdown] = useState(0);
  const navigate = useNavigate();

  // Validation schema
  const validationSchema = Yup.object({
    email: Yup.string()
      .email("Please enter a valid email address")
      .required("Email address is required"),
  });

  // Form handling
  const formik = useFormik({
    initialValues: {
      email: "",
    },
    validationSchema,
    onSubmit: async (values) => {
      setIsLoading(true);
      try {
        // Simulate API call
        await new Promise((resolve) => setTimeout(resolve, 2000));

        console.log("Password reset requested for:", values.email);

        // Move to next step
        setStep(2);
        setEmailSent(true);
        startCountdown();

        toast.success("Password reset instructions sent to your email!");
      } catch (error) {
        toast.error("Failed to send reset email. Please try again.");
      } finally {
        setIsLoading(false);
      }
    },
  });

  // Countdown timer for resend functionality
  const startCountdown = () => {
    setCountdown(60);
    const timer = setInterval(() => {
      setCountdown((prev) => {
        if (prev <= 1) {
          clearInterval(timer);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  };

  // Resend email function
  const handleResendEmail = async () => {
    if (countdown > 0) return;

    setIsLoading(true);
    try {
      // Simulate API call
      await new Promise((resolve) => setTimeout(resolve, 1500));

      startCountdown();
      toast.success("Reset email sent again!");
    } catch (error) {
      toast.error("Failed to resend email. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Step 1: Email Input Form
  const renderEmailForm = () => (
    <div className="forgot-password-form">
      <div className="text-center mb-4">
        <div className="forgot-password-icon mb-3">
          <FaShieldAlt size={48} className="text-primary" />
        </div>
        <h2 className="fw-bold mb-2">Forgot Password?</h2>
        <p className="text-muted">
          No worries! Enter your email address and we'll send you instructions to reset your password.
        </p>
      </div>

      <form onSubmit={formik.handleSubmit}>
        <div className="mb-4">
          <label htmlFor="email" className="form-label">
            <FaEnvelope className="me-2" size={14} />
            Email Address
          </label>
          <div className="position-relative">
            <input
              type="email"
              className={`form-control ps-5 ${
                formik.touched.email && formik.errors.email ? "is-invalid" : ""
              }`}
              id="email"
              name="email"
              value={formik.values.email}
              onChange={formik.handleChange}
              onBlur={formik.handleBlur}
              placeholder="Enter your registered email address"
              disabled={isLoading}
            />
            <FaEnvelope
              className="position-absolute text-muted"
              style={{
                left: "1rem",
                top: "50%",
                transform: "translateY(-50%)",
                fontSize: "0.875rem",
              }}
            />
            {formik.touched.email && formik.errors.email && (
              <div className="invalid-feedback">{formik.errors.email}</div>
            )}
          </div>
        </div>

        <button
          type="submit"
          className="btn btn-primary w-100 py-2 mb-3"
          disabled={isLoading || !formik.isValid}
        >
          {isLoading ? (
            <>
              <FaSpinner className="spinner-border spinner-border-sm me-2" />
              Sending Instructions...
            </>
          ) : (
            <>
              <FaPaperPlane className="me-2" />
              Send Reset Instructions
            </>
          )}
        </button>

        <div className="text-center">
          <Link
            to={ROUTES.LOGIN}
            className="text-decoration-none d-inline-flex align-items-center"
          >
            <FaArrowLeft className="me-2" size={14} />
            Back to Sign In
          </Link>
        </div>
      </form>
    </div>
  );

  // Step 2: Email Sent Confirmation
  const renderEmailSent = () => (
    <div className="forgot-password-form text-center">
      <div className="mb-4">
        <div className="email-sent-icon mb-3">
          <FaCheckCircle size={64} className="text-success" />
        </div>
        <h2 className="fw-bold mb-2">Check Your Email</h2>
        <p className="text-muted mb-3">
          We've sent password reset instructions to:
        </p>
        <div className="email-display bg-light rounded p-3 mb-4">
          <strong className="text-primary">{formik.values.email}</strong>
        </div>
      </div>

      <div className="instructions-card bg-light rounded p-4 mb-4">
        <h6 className="fw-semibold mb-3">What's next?</h6>
        <div className="instruction-steps">
          <div className="d-flex align-items-start mb-2">
            <span className="badge bg-primary rounded-circle me-3 mt-1">1</span>
            <span className="small">Check your email inbox (and spam folder)</span>
          </div>
          <div className="d-flex align-items-start mb-2">
            <span className="badge bg-primary rounded-circle me-3 mt-1">2</span>
            <span className="small">Click the reset link in the email</span>
          </div>
          <div className="d-flex align-items-start">
            <span className="badge bg-primary rounded-circle me-3 mt-1">3</span>
            <span className="small">Create your new password</span>
          </div>
        </div>
      </div>

      <div className="resend-section mb-4">
        <p className="small text-muted mb-2">Didn't receive the email?</p>
        <button
          type="button"
          className="btn btn-outline-primary"
          onClick={handleResendEmail}
          disabled={countdown > 0 || isLoading}
        >
          {isLoading ? (
            <>
              <FaSpinner className="spinner-border spinner-border-sm me-2" />
              Sending...
            </>
          ) : countdown > 0 ? (
            <>
              <FaClock className="me-2" />
              Resend in {countdown}s
            </>
          ) : (
            <>
              <FaRedo className="me-2" />
              Resend Email
            </>
          )}
        </button>
      </div>

      <div className="text-center">
        <Link
          to={ROUTES.LOGIN}
          className="text-decoration-none d-inline-flex align-items-center"
        >
          <FaArrowLeft className="me-2" size={14} />
          Back to Sign In
        </Link>
      </div>
    </div>
  );

  return (
    <div className="forgot-password-container">
      {step === 1 && renderEmailForm()}
      {step === 2 && renderEmailSent()}
    </div>
  );
};

export default ForgotPassword;
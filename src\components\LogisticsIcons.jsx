import React from "react";

// Custom SVG Icons for Logistics Theme
export const TruckIcon = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor" 
    className={className}
  >
    <path d="M20 8h-3V4H3c-1.1 0-2 .9-2 2v11h2c0 1.66 1.34 3 3 3s3-1.34 3-3h6c0 1.66 1.34 3 3 3s3-1.34 3-3h2v-5l-3-4zM6 18.5c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5zm13.5-9l1.96 2.5H17V9.5h2.5zm-1.5 9c-.83 0-1.5-.67-1.5-1.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5-.67 1.5-1.5 1.5z"/>
  </svg>
);

export const DeliveryIcon = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor" 
    className={className}
  >
    <path d="M19 7c0-1.1-.9-2-2-2h-3v2h3v2.65L13.52 14H10V9H6c-2.21 0-4 1.79-4 4v3h2c0 1.66 1.34 3 3 3s3-1.34 3-3h4.48L19 10.35V7zM7 17c-.55 0-1-.45-1-1s.45-1 1-1 1 .45 1 1-.45 1-1 1z"/>
    <path d="M5 6h5v2H5zm0-2h7v2H5z"/>
  </svg>
);

export const WarehouseIcon = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor" 
    className={className}
  >
    <path d="M12 3L2 12h3v8h14v-8h3L12 3zm0 2.84L18.16 12H17v6H7v-6H5.84L12 5.84z"/>
    <path d="M9 13h2v4H9zm4 0h2v4h-2z"/>
  </svg>
);

export const RouteIcon = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor" 
    className={className}
  >
    <path d="M19 15l-6 6-1.42-1.42L15.17 16H4V4h2v10h9.17l-3.59-3.58L13 9l6 6z"/>
    <circle cx="7.5" cy="6.5" r="1.5"/>
    <circle cx="7.5" cy="11.5" r="1.5"/>
    <circle cx="16.5" cy="19.5" r="1.5"/>
  </svg>
);

export const PackageIcon = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor" 
    className={className}
  >
    <path d="M12 2l3 3h4v14H5V5h4l3-3zm0 2.83L10.83 6H7v12h10V6h-3.83L12 4.83z"/>
    <path d="M12 8l-2 2v4l2-2 2 2v-4l-2-2z"/>
  </svg>
);

export const TrackingIcon = ({ size = 24, className = "" }) => (
  <svg 
    width={size} 
    height={size} 
    viewBox="0 0 24 24" 
    fill="currentColor" 
    className={className}
  >
    <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
    <circle cx="12" cy="9" r="1"/>
  </svg>
);

// Animated Background Pattern Component
export const LogisticsPattern = () => (
  <div className="logistics-pattern">
    <svg 
      className="pattern-svg" 
      width="100%" 
      height="100%" 
      viewBox="0 0 100 100" 
      preserveAspectRatio="none"
    >
      <defs>
        <pattern id="logistics-grid" width="20" height="20" patternUnits="userSpaceOnUse">
          <path d="M 20 0 L 0 0 0 20" fill="none" stroke="rgba(255,255,255,0.1)" strokeWidth="0.5"/>
        </pattern>
      </defs>
      <rect width="100%" height="100%" fill="url(#logistics-grid)" />
    </svg>
  </div>
);

// Company Logo Component
export const TriTrackzLogo = ({ size = 60, className = "" }) => (
  <div className={`tritrackz-logo ${className}`} style={{ width: size, height: size }}>
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 60 60" 
      fill="none"
    >
      <rect width="60" height="60" rx="12" fill="url(#logoGradient)"/>
      <defs>
        <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" stopColor="#3b82f6"/>
          <stop offset="100%" stopColor="#1e40af"/>
        </linearGradient>
      </defs>
      <text 
        x="30" 
        y="38" 
        textAnchor="middle" 
        fill="white" 
        fontSize="20" 
        fontWeight="bold" 
        fontFamily="Arial, sans-serif"
      >
        TT
      </text>
      <circle cx="45" cy="15" r="3" fill="#f97316" opacity="0.8"/>
      <circle cx="15" cy="45" r="2" fill="#10b981" opacity="0.6"/>
    </svg>
  </div>
);

/* Home Component Styles */

/* Dashboard Cards */
.dashboard-home .card {
  transition: all 0.3s ease;
  border-radius: 12px;
}

.dashboard-home .card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

/* Stats Cards */
.dashboard-home .card-body {
  padding: 1.5rem;
}

.dashboard-home .h4 {
  font-size: 2rem;
  font-weight: 700;
}

/* Table Styles */
.dashboard-home .table {
  font-size: 0.9rem;
}

.dashboard-home .table th {
  font-weight: 600;
  color: var(--dark-grey);
  font-size: 0.85rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  padding: 1rem 1.5rem;
}

.dashboard-home .table td {
  padding: 1rem 1.5rem;
  vertical-align: middle;
}

.dashboard-home .table-hover tbody tr:hover {
  background-color: var(--light-grey);
}

/* Progress Bar */
.dashboard-home .progress {
  background-color: #e9ecef;
  border-radius: 10px;
}

.dashboard-home .progress-bar {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border-radius: 10px;
}

/* Badges */
.dashboard-home .badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;
}

/* Quick Actions Buttons */
.dashboard-home .btn-outline-primary,
.dashboard-home .btn-outline-success,
.dashboard-home .btn-outline-info,
.dashboard-home .btn-outline-warning {
  border-width: 2px;
  font-weight: 500;
  padding: 0.75rem 1rem;
  transition: all 0.3s ease;
}

.dashboard-home .btn-outline-primary:hover {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border-color: var(--primary-blue);
  transform: translateX(4px);
}

/* Card Headers */
.dashboard-home .card-header {
  background: var(--white) !important;
  border-bottom: 1px solid #e9ecef !important;
  padding: 1.25rem 1.5rem;
}

.dashboard-home .card-header h5 {
  color: var(--dark-grey);
  font-weight: 600;
}

/* Welcome Header */
.dashboard-home h1 {
  background: linear-gradient(135deg, var(--dark-grey) 0%, var(--primary-blue) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Action Buttons in Header */
.dashboard-home .btn-primary {
  background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
  border: none;
  font-weight: 500;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.dashboard-home .btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 130, 246, 0.3);
}

.dashboard-home .btn-outline-primary {
  border-color: var(--secondary-blue);
  color: var(--secondary-blue);
}

/* Responsive Adjustments */
@media (max-width: 768px) {
  .dashboard-home .d-flex.justify-content-between {
    flex-direction: column;
    gap: 1rem;
  }
  
  .dashboard-home .d-flex.gap-2 {
    justify-content: stretch;
  }
  
  .dashboard-home .btn {
    flex: 1;
  }
  
  .dashboard-home .table-responsive {
    font-size: 0.85rem;
  }
  
  .dashboard-home .card-body {
    padding: 1rem;
  }
}

@media (max-width: 576px) {
  .dashboard-home .col-xl-3 {
    margin-bottom: 1rem;
  }
  
  .dashboard-home .h4 {
    font-size: 1.5rem;
  }
  
  .dashboard-home .card-header {
    padding: 1rem;
  }
  
  .dashboard-home .table th,
  .dashboard-home .table td {
    padding: 0.75rem 1rem;
  }
}

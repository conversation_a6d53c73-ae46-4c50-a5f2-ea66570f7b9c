import React from "react";
import Breadcrumb from "../Breadcrumb";
import SearchBar from "../SearchBar";
import UserMenu from "../UserMenu";
import {
  FaBars,
  FaTimes
} from "react-icons/fa";

const TopHeader = ({
  collapsed,
  onToggleSidebar,
  onToggleMobileSidebar,
  onLogout
}) => {
  const handleSearch = (searchTerm) => {
    console.log("Search:", searchTerm);
    // Implement search functionality here
  };

  return (
    <header className="top-header bg-white border-bottom px-4 py-3">
      <div className="d-flex align-items-center justify-content-between">
        {/* Left Section - Menu Toggle & Breadcrumb */}
        <div className="d-flex align-items-center">
          <button
            className="btn btn-link text-muted p-0 me-3 d-lg-none"
            onClick={onToggleMobileSidebar}
            title="Toggle Mobile Menu"
          >
            <FaBars size={20} />
          </button>
          <button
            className="btn btn-link text-muted p-0 me-3 d-none d-lg-block"
            onClick={onToggleSidebar}
            title={collapsed ? "Expand Sidebar" : "Collapse Sidebar"}
          >
            {collapsed ? <FaBars size={20} /> : <FaTimes size={20} />}
          </button>
          <Breadcrumb />
        </div>

        {/* Center Section - Search */}
        <SearchBar onSearch={handleSearch} />

        {/* Right Section - Notifications & User Menu */}
        <UserMenu onLogout={onLogout} />
      </div>
    </header>
  );
};

export default TopHeader;

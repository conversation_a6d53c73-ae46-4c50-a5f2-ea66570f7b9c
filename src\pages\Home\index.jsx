import React from 'react';
import {
  FaTruck,
  FaBoxOpen,
  FaWarehouse,
  FaUsers,
  FaChartLine,
  FaMapMarkedAlt,
  FaArrowUp,
  FaArrowDown
} from 'react-icons/fa';

const Home = () => {
  // Mock data for dashboard
  const stats = [
    {
      title: 'Active Shipments',
      value: '1,247',
      change: '+12%',
      trend: 'up',
      icon: FaBoxOpen,
      color: 'primary'
    },
    {
      title: 'Fleet Vehicles',
      value: '89',
      change: '+3%',
      trend: 'up',
      icon: FaTruck,
      color: 'success'
    },
    {
      title: 'Warehouses',
      value: '24',
      change: '0%',
      trend: 'neutral',
      icon: FaWarehouse,
      color: 'info'
    },
    {
      title: 'Active Customers',
      value: '2,156',
      change: '+8%',
      trend: 'up',
      icon: FaUsers,
      color: 'warning'
    }
  ];

  const recentShipments = [
    { id: 'TT001', customer: 'ABC Corp', destination: 'Mumbai', status: 'In Transit', progress: 75 },
    { id: 'TT002', customer: 'XYZ Ltd', destination: 'Delhi', status: 'Delivered', progress: 100 },
    { id: 'TT003', customer: 'PQR Inc', destination: 'Bangalore', status: 'Processing', progress: 25 },
    { id: 'TT004', customer: 'LMN Co', destination: 'Chennai', status: 'In Transit', progress: 60 },
  ];

  return (
    <div className="dashboard-home">
      {/* Welcome Header */}
      <div className="row mb-4">
        <div className="col-12">
          <div className="d-flex justify-content-between align-items-center">
            <div>
              <h1 className="h3 mb-1 text-dark fw-bold">Welcome to TriTrackz Dashboard</h1>
              <p className="text-muted mb-0">Monitor your logistics operations in real-time</p>
            </div>
            <div className="d-flex gap-2">
              <button className="btn btn-outline-primary">
                <FaMapMarkedAlt className="me-2" />
                Track Shipment
              </button>
              <button className="btn btn-primary">
                <FaBoxOpen className="me-2" />
                New Shipment
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="row mb-4">
        {stats.map((stat, index) => (
          <div key={index} className="col-xl-3 col-md-6 mb-3">
            <div className="card border-0 shadow-sm h-100">
              <div className="card-body">
                <div className="d-flex align-items-center justify-content-between">
                  <div>
                    <div className="text-muted small mb-1">{stat.title}</div>
                    <div className="h4 mb-0 fw-bold text-dark">{stat.value}</div>
                    <div className={`small d-flex align-items-center mt-1 ${
                      stat.trend === 'up' ? 'text-success' :
                      stat.trend === 'down' ? 'text-danger' : 'text-muted'
                    }`}>
                      {stat.trend === 'up' && <FaArrowUp className="me-1" size={12} />}
                      {stat.trend === 'down' && <FaArrowDown className="me-1" size={12} />}
                      {stat.change} from last month
                    </div>
                  </div>
                  <div className={`bg-${stat.color} bg-opacity-10 rounded-3 p-3`}>
                    <stat.icon className={`text-${stat.color}`} size={24} />
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Main Content Row */}
      <div className="row">
        {/* Recent Shipments */}
        <div className="col-lg-8 mb-4">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-white border-bottom">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0 fw-semibold">Recent Shipments</h5>
                <button className="btn btn-sm btn-outline-primary">View All</button>
              </div>
            </div>
            <div className="card-body p-0">
              <div className="table-responsive">
                <table className="table table-hover mb-0">
                  <thead className="table-light">
                    <tr>
                      <th className="border-0 fw-semibold">Shipment ID</th>
                      <th className="border-0 fw-semibold">Customer</th>
                      <th className="border-0 fw-semibold">Destination</th>
                      <th className="border-0 fw-semibold">Status</th>
                      <th className="border-0 fw-semibold">Progress</th>
                    </tr>
                  </thead>
                  <tbody>
                    {recentShipments.map((shipment) => (
                      <tr key={shipment.id}>
                        <td className="fw-semibold text-primary">{shipment.id}</td>
                        <td>{shipment.customer}</td>
                        <td>{shipment.destination}</td>
                        <td>
                          <span className={`badge ${
                            shipment.status === 'Delivered' ? 'bg-success' :
                            shipment.status === 'In Transit' ? 'bg-primary' :
                            'bg-warning'
                          }`}>
                            {shipment.status}
                          </span>
                        </td>
                        <td>
                          <div className="d-flex align-items-center">
                            <div className="progress me-2" style={{ width: '100px', height: '6px' }}>
                              <div
                                className="progress-bar"
                                style={{ width: `${shipment.progress}%` }}
                              ></div>
                            </div>
                            <small className="text-muted">{shipment.progress}%</small>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Actions & Analytics */}
        <div className="col-lg-4">
          {/* Quick Actions */}
          <div className="card border-0 shadow-sm mb-4">
            <div className="card-header bg-white border-bottom">
              <h5 className="mb-0 fw-semibold">Quick Actions</h5>
            </div>
            <div className="card-body">
              <div className="d-grid gap-2">
                <button className="btn btn-outline-primary text-start">
                  <FaBoxOpen className="me-3" />
                  Create New Shipment
                </button>
                <button className="btn btn-outline-success text-start">
                  <FaTruck className="me-3" />
                  Assign Vehicle
                </button>
                <button className="btn btn-outline-info text-start">
                  <FaMapMarkedAlt className="me-3" />
                  Track Location
                </button>
                <button className="btn btn-outline-warning text-start">
                  <FaChartLine className="me-3" />
                  View Reports
                </button>
              </div>
            </div>
          </div>

          {/* Performance Chart Placeholder */}
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-white border-bottom">
              <h5 className="mb-0 fw-semibold">Performance Overview</h5>
            </div>
            <div className="card-body text-center py-5">
              <FaChartLine size={48} className="text-muted mb-3" />
              <p className="text-muted mb-0">Analytics chart will be displayed here</p>
              <small className="text-muted">Integration with chart library pending</small>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;